<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Zoho Payment</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
        }
        
        .payment-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .payment-header {
            background: #1976d2;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .payment-amount {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .payment-description {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .payment-content {
            padding: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1976d2;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .success {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .retry-button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 15px;
        }
        
        .retry-button:hover {
            background: #1565c0;
        }
        
        /* Hide Zoho branding if needed */
        .zpayments-container .zpayments-footer {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="payment-header">
            <div class="payment-amount" id="paymentAmount">₹0.00</div>
            <div class="payment-description" id="paymentDescription">Processing payment...</div>
        </div>
        
        <div class="payment-content">
            <div id="loadingSection" class="loading">
                <div class="loading-spinner"></div>
                <div>Loading payment options...</div>
            </div>
            
            <div id="errorSection" class="error" style="display: none;">
                <div id="errorMessage">An error occurred while loading payment options.</div>
                <button class="retry-button" onclick="retryPayment()">Retry</button>
            </div>
            
            <div id="successSection" class="success" style="display: none;">
                <div>Payment completed successfully!</div>
            </div>
            
            <!-- Zoho Payments widget will be rendered here -->
            <div id="paymentWidget"></div>
        </div>
    </div>

    <!-- Zoho Payments JavaScript SDK -->
    <script src="https://js.zoho.com/v1/zpayments.js"></script>
    
    <script>
        // Global variables
        let zpaymentsInstance = null;
        let paymentConfig = {};
        
        // Initialize payment when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Get payment configuration from URL parameters or postMessage
            initializePayment();
        });
        
        function initializePayment() {
            try {
                // Listen for payment configuration from Flutter
                window.addEventListener('message', function(event) {
                    if (event.data && event.data.type === 'PAYMENT_CONFIG') {
                        paymentConfig = event.data.config;
                        setupPayment();
                    }
                });
                
                // Request payment configuration from Flutter
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('getPaymentConfig');
                } else {
                    // Fallback: try to get config from URL parameters
                    const urlParams = new URLSearchParams(window.location.search);
                    if (urlParams.has('sessionId')) {
                        paymentConfig = {
                            sessionId: urlParams.get('sessionId'),
                            amount: urlParams.get('amount') || '0',
                            currency: urlParams.get('currency') || 'INR',
                            description: urlParams.get('description') || 'Payment',
                            accountId: urlParams.get('accountId') || '',
                            apiKey: urlParams.get('apiKey') || ''
                        };
                        setupPayment();
                    }
                }
            } catch (error) {
                console.error('Error initializing payment:', error);
                showError('Failed to initialize payment: ' + error.message);
            }
        }
        
        function setupPayment() {
            try {
                // Update UI with payment details
                document.getElementById('paymentAmount').textContent = 
                    '₹' + parseFloat(paymentConfig.amount || 0).toFixed(2);
                document.getElementById('paymentDescription').textContent = 
                    paymentConfig.description || 'Complete your payment';
                
                // Initialize Zoho Payments
                if (typeof ZPayments !== 'undefined') {
                    zpaymentsInstance = new ZPayments({
                        account_id: paymentConfig.accountId,
                        api_key: paymentConfig.apiKey
                    });
                    
                    requestPaymentMethod();
                } else {
                    throw new Error('Zoho Payments SDK not loaded');
                }
            } catch (error) {
                console.error('Error setting up payment:', error);
                showError('Failed to setup payment: ' + error.message);
            }
        }
        
        function requestPaymentMethod() {
            try {
                const paymentData = {
                    session_id: paymentConfig.sessionId,
                    amount: parseFloat(paymentConfig.amount),
                    currency: paymentConfig.currency || 'INR',
                    description: paymentConfig.description || 'Payment',
                    customer_details: {
                        name: paymentConfig.customerName || '',
                        email: paymentConfig.customerEmail || '',
                        phone: paymentConfig.customerPhone || ''
                    }
                };
                
                zpaymentsInstance.requestPaymentMethod(paymentData, {
                    onSuccess: function(response) {
                        console.log('Payment successful:', response);
                        showSuccess();
                        notifyFlutter('success', response);
                    },
                    onFailure: function(error) {
                        console.error('Payment failed:', error);
                        showError('Payment failed: ' + (error.message || 'Unknown error'));
                        notifyFlutter('failure', error);
                    },
                    onCancel: function() {
                        console.log('Payment cancelled by user');
                        notifyFlutter('cancel', null);
                    }
                }, 'paymentWidget');
                
                // Hide loading section
                document.getElementById('loadingSection').style.display = 'none';
                
            } catch (error) {
                console.error('Error requesting payment method:', error);
                showError('Failed to load payment options: ' + error.message);
            }
        }
        
        function showError(message) {
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('successSection').style.display = 'none';
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorSection').style.display = 'block';
        }
        
        function showSuccess() {
            document.getElementById('loadingSection').style.display = 'none';
            document.getElementById('errorSection').style.display = 'none';
            document.getElementById('successSection').style.display = 'block';
        }
        
        function retryPayment() {
            document.getElementById('errorSection').style.display = 'none';
            document.getElementById('loadingSection').style.display = 'block';
            setTimeout(requestPaymentMethod, 1000);
        }
        
        function notifyFlutter(status, data) {
            try {
                // Notify Flutter WebView about payment result
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.callHandler('paymentResult', {
                        status: status,
                        data: data
                    });
                } else {
                    // Fallback: use postMessage
                    window.parent.postMessage({
                        type: 'PAYMENT_RESULT',
                        status: status,
                        data: data
                    }, '*');
                }
            } catch (error) {
                console.error('Error notifying Flutter:', error);
            }
        }
        
        // Handle back button or page unload
        window.addEventListener('beforeunload', function() {
            if (zpaymentsInstance) {
                notifyFlutter('cancel', null);
            }
        });
    </script>
</body>
</html>
