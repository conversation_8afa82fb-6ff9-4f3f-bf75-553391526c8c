/// Configuration for Zoho Payments SDK integration
class PaymentConfig {
  final String sessionId;
  final double amount;
  final String currency;
  final String description;
  final String invoiceNumber;
  final String customerId;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String accountId;
  final String apiKey;

  const PaymentConfig({
    required this.sessionId,
    required this.amount,
    required this.currency,
    required this.description,
    required this.invoiceNumber,
    required this.customerId,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    required this.accountId,
    required this.apiKey,
  });

  /// Convert to JSON for JavaScript SDK
  Map<String, dynamic> toJson() {
    return {
      'sessionId': sessionId,
      'amount': amount.toString(),
      'currency': currency,
      'description': description,
      'invoiceNumber': invoiceNumber,
      'customerId': customerId,
      'customerName': customerName,
      'customerEmail': customerEmail,
      'customerPhone': customerPhone,
      'accountId': accountId,
      'apiKey': apiKey,
    };
  }

  /// Create from payment request and session response
  factory PaymentConfig.fromPaymentData({
    required String sessionId,
    required double amount,
    required String currency,
    required String description,
    required String invoiceNumber,
    required String customerId,
    String? customerName,
    String? customerEmail,
    String? customerPhone,
    String? accountId,
    String? apiKey,
  }) {
    return PaymentConfig(
      sessionId: sessionId,
      amount: amount,
      currency: currency,
      description: description,
      invoiceNumber: invoiceNumber,
      customerId: customerId,
      customerName: customerName,
      customerEmail: customerEmail,
      customerPhone: customerPhone,
      accountId: accountId ?? _getDefaultAccountId(),
      apiKey: apiKey ?? _getDefaultApiKey(),
    );
  }

  /// Get default Zoho account ID from configuration
  static String _getDefaultAccountId() {
    // Import ZohoConfig to avoid circular dependency
    return const String.fromEnvironment(
      'ZOHO_ACCOUNT_ID',
      defaultValue: 'YOUR_ZOHO_ACCOUNT_ID', // Replace with actual account ID
    );
  }

  /// Get default Zoho API key from configuration
  static String _getDefaultApiKey() {
    // Import ZohoConfig to avoid circular dependency
    return const String.fromEnvironment(
      'ZOHO_API_KEY',
      defaultValue: 'YOUR_ZOHO_API_KEY', // Replace with actual API key
    );
  }

  /// Create URL parameters for the payment widget
  String toUrlParameters() {
    final params = <String, String>{
      'sessionId': sessionId,
      'amount': amount.toString(),
      'currency': currency,
      'description': Uri.encodeComponent(description),
      'accountId': accountId,
      'apiKey': apiKey,
    };

    if (customerName != null) {
      params['customerName'] = Uri.encodeComponent(customerName!);
    }
    if (customerEmail != null) {
      params['customerEmail'] = Uri.encodeComponent(customerEmail!);
    }
    if (customerPhone != null) {
      params['customerPhone'] = Uri.encodeComponent(customerPhone!);
    }

    return params.entries.map((e) => '${e.key}=${e.value}').join('&');
  }

  @override
  String toString() {
    return 'PaymentConfig(sessionId: $sessionId, amount: $amount, currency: $currency)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentConfig &&
        other.sessionId == sessionId &&
        other.amount == amount &&
        other.currency == currency &&
        other.description == description &&
        other.invoiceNumber == invoiceNumber &&
        other.customerId == customerId &&
        other.customerName == customerName &&
        other.customerEmail == customerEmail &&
        other.customerPhone == customerPhone &&
        other.accountId == accountId &&
        other.apiKey == apiKey;
  }

  @override
  int get hashCode {
    return Object.hash(
      sessionId,
      amount,
      currency,
      description,
      invoiceNumber,
      customerId,
      customerName,
      customerEmail,
      customerPhone,
      accountId,
      apiKey,
    );
  }
}

/// Payment result from Zoho Payments SDK
class PaymentResult {
  final PaymentStatus status;
  final String? transactionId;
  final String? paymentId;
  final String? errorMessage;
  final Map<String, dynamic>? additionalData;

  const PaymentResult({
    required this.status,
    this.transactionId,
    this.paymentId,
    this.errorMessage,
    this.additionalData,
  });

  factory PaymentResult.success({
    required String transactionId,
    String? paymentId,
    Map<String, dynamic>? additionalData,
  }) {
    return PaymentResult(
      status: PaymentStatus.success,
      transactionId: transactionId,
      paymentId: paymentId,
      additionalData: additionalData,
    );
  }

  factory PaymentResult.failure({
    required String errorMessage,
    Map<String, dynamic>? additionalData,
  }) {
    return PaymentResult(
      status: PaymentStatus.failure,
      errorMessage: errorMessage,
      additionalData: additionalData,
    );
  }

  factory PaymentResult.cancelled() {
    return const PaymentResult(status: PaymentStatus.cancelled);
  }

  factory PaymentResult.fromJson(Map<String, dynamic> json) {
    final statusString = json['status'] as String?;
    final status = PaymentStatus.values.firstWhere(
      (e) => e.name == statusString,
      orElse: () => PaymentStatus.failure,
    );

    return PaymentResult(
      status: status,
      transactionId: json['transactionId'] as String?,
      paymentId: json['paymentId'] as String?,
      errorMessage: json['errorMessage'] as String?,
      additionalData: json['additionalData'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status.name,
      'transactionId': transactionId,
      'paymentId': paymentId,
      'errorMessage': errorMessage,
      'additionalData': additionalData,
    };
  }

  @override
  String toString() {
    return 'PaymentResult(status: $status, transactionId: $transactionId, errorMessage: $errorMessage)';
  }
}

enum PaymentStatus { success, failure, cancelled }
