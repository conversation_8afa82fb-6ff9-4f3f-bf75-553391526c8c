import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import 'dart:developer' as developer;

import '../../domain/entities/payments/payment_config.dart';

class ZohoPaymentWebView extends StatefulWidget {
  final PaymentConfig paymentConfig;
  final Function(bool success, String? transactionId) onPaymentComplete;

  const ZohoPaymentWebView({
    super.key,
    required this.paymentConfig,
    required this.onPaymentComplete,
  });

  @override
  State<ZohoPaymentWebView> createState() => _ZohoPaymentWebViewState();
}

class _ZohoPaymentWebViewState extends State<ZohoPaymentWebView> {
  late WebViewController _controller;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    try {
      // Construct payment URL from PaymentConfig
      final paymentUrl = _constructPaymentUrl();
      // Validate and fix the payment URL
      final validatedUrl = _validateAndFixPaymentUrl(paymentUrl);

      _controller =
          WebViewController()
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..addJavaScriptChannel(
              'PaymentHandler',
              onMessageReceived: (JavaScriptMessage message) {
                _handlePaymentMessage(message.message);
              },
            )
            ..setNavigationDelegate(
              NavigationDelegate(
                onPageStarted: (String url) {
                  setState(() {
                    _isLoading = true;
                    _errorMessage = null;
                  });
                },
                onPageFinished: (String url) {
                  setState(() {
                    _isLoading = false;
                  });
                  // Send payment configuration to the web page
                  _sendPaymentConfig();
                },
                onWebResourceError: (WebResourceError error) {
                  setState(() {
                    _isLoading = false;
                    _errorMessage =
                        'Failed to load payment page: ${error.description}';
                  });
                },
                onNavigationRequest: (NavigationRequest request) {
                  // Check for success or failure redirects
                  if (request.url.contains('payment/success') ||
                      request.url.contains('success') ||
                      request.url.contains('completed')) {
                    // Extract transaction ID if available in URL
                    final uri = Uri.parse(request.url);
                    final transactionId =
                        uri.queryParameters['transaction_id'] ??
                        uri.queryParameters['txn_id'] ??
                        uri.queryParameters['id'];
                    widget.onPaymentComplete(true, transactionId);
                    return NavigationDecision.prevent;
                  } else if (request.url.contains('payment/failure') ||
                      request.url.contains('failure') ||
                      request.url.contains('failed') ||
                      request.url.contains('cancel')) {
                    widget.onPaymentComplete(false, null);
                    return NavigationDecision.prevent;
                  }
                  return NavigationDecision.navigate;
                },
              ),
            )
            ..loadRequest(Uri.parse(validatedUrl));
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Invalid payment URL: $e';
      });
    }
  }

  /// Constructs the payment URL from PaymentConfig
  String _constructPaymentUrl() {
    // Use the local HTML file with Zoho Payments JavaScript SDK
    // The HTML file will receive the payment configuration via postMessage
    final baseUrl =
        'file:///android_asset/flutter_assets/assets/html/zoho_payment_widget.html';

    // Add session ID as URL parameter for fallback
    final urlParams = widget.paymentConfig.toUrlParameters();

    return '$baseUrl?$urlParams';
  }

  /// Validates and fixes the payment URL to ensure it has a proper scheme
  String _validateAndFixPaymentUrl(String paymentUrl) {
    developer.log(
      'Original payment URL: "$paymentUrl"',
      name: 'ZohoPaymentWebView',
    );

    if (paymentUrl.isEmpty) {
      throw ArgumentError('Payment URL cannot be empty');
    }

    // Remove any leading/trailing whitespace
    final trimmedUrl = paymentUrl.trim();
    developer.log(
      'Trimmed payment URL: "$trimmedUrl"',
      name: 'ZohoPaymentWebView',
    );

    if (trimmedUrl.isEmpty) {
      throw ArgumentError('Payment URL cannot be empty after trimming');
    }

    // Check for obviously invalid URLs
    if (trimmedUrl.contains(' ') && !trimmedUrl.startsWith('http')) {
      throw ArgumentError('Invalid URL format: contains spaces');
    }

    String finalUrl;

    // Check if URL already has a scheme
    if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
      finalUrl = trimmedUrl;
      developer.log('URL already has scheme', name: 'ZohoPaymentWebView');
    }
    // If URL starts with '//', add https:
    else if (trimmedUrl.startsWith('//')) {
      finalUrl = 'https:$trimmedUrl';
      developer.log(
        'Added https: to protocol-relative URL',
        name: 'ZohoPaymentWebView',
      );
    }
    // If URL starts with '/', assume it's a relative path and add base URL
    else if (trimmedUrl.startsWith('/')) {
      finalUrl =
          'https://aquapartner-fyhcb8abcbazfyef.centralindia-01.azurewebsites.net$trimmedUrl';
      developer.log(
        'Added base URL to relative path',
        name: 'ZohoPaymentWebView',
      );
    }
    // If URL doesn't start with scheme, assume https://
    else {
      // Additional validation for domain-like strings
      if (!trimmedUrl.contains('.') && !trimmedUrl.contains('/')) {
        throw ArgumentError('Invalid URL format: missing domain or path');
      }
      finalUrl = 'https://$trimmedUrl';
      developer.log('Added https:// scheme to URL', name: 'ZohoPaymentWebView');
    }

    developer.log('Final payment URL: "$finalUrl"', name: 'ZohoPaymentWebView');

    // Validate the final URL can be parsed and has valid components
    try {
      final uri = Uri.parse(finalUrl);
      if (uri.scheme.isEmpty ||
          (!uri.hasAuthority && !uri.path.startsWith('/'))) {
        throw ArgumentError('Invalid URL structure');
      }
      developer.log('URL validation successful', name: 'ZohoPaymentWebView');
    } catch (e) {
      developer.log('URL validation failed: $e', name: 'ZohoPaymentWebView');
      throw ArgumentError(
        'Invalid URL format after processing: $finalUrl - $e',
      );
    }

    return finalUrl;
  }

  /// Sends payment configuration to the WebView
  void _sendPaymentConfig() {
    try {
      final configJson = jsonEncode(widget.paymentConfig.toJson());
      final script = '''
        window.postMessage({
          type: 'PAYMENT_CONFIG',
          config: $configJson
        }, '*');
      ''';

      _controller.runJavaScript(script);
      developer.log(
        'Payment config sent to WebView',
        name: 'ZohoPaymentWebView',
      );
    } catch (e) {
      developer.log(
        'Error sending payment config: $e',
        name: 'ZohoPaymentWebView',
      );
    }
  }

  /// Handles messages from the WebView JavaScript
  void _handlePaymentMessage(String message) {
    try {
      final data = jsonDecode(message);
      developer.log(
        'Received payment message: $data',
        name: 'ZohoPaymentWebView',
      );

      if (data['type'] == 'PAYMENT_SUCCESS') {
        final transactionId = data['transactionId'] as String?;
        widget.onPaymentComplete(true, transactionId);
      } else if (data['type'] == 'PAYMENT_FAILURE') {
        widget.onPaymentComplete(false, null);
      } else if (data['type'] == 'PAYMENT_CANCELLED') {
        widget.onPaymentComplete(false, null);
      }
    } catch (e) {
      developer.log(
        'Error handling payment message: $e',
        name: 'ZohoPaymentWebView',
      );
      widget.onPaymentComplete(false, null);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            widget.onPaymentComplete(false, null);
            Navigator.of(context).pop();
          },
        ),
      ),
      body:
          _errorMessage != null
              ? _buildErrorView()
              : Stack(
                children: [
                  WebViewWidget(controller: _controller),
                  if (_isLoading)
                    const Center(child: CircularProgressIndicator()),
                ],
              ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Payment Error',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                  _isLoading = true;
                });
                _initializeWebView();
              },
              child: const Text('Retry'),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                widget.onPaymentComplete(false, null);
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }
}
