name: aquapartner
description: "One Stop Solution for your Aquaculture Needs"

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.3

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^3.13.0
  firebase_auth: ^5.5.3
  firebase_analytics: ^11.4.5
  firebase_app_check: ^0.3.2+5
  firebase_performance: ^0.10.1+2
  firebase_remote_config: ^5.4.3
  url_launcher: ^6.3.1
  package_info_plus: ^8.3.0
  get_it: ^8.0.3
  flutter_bloc: ^9.1.1
  collection: ^1.19.1
  equatable: ^2.0.7
  path: ^1.9.0
  path_provider: ^2.1.5
  pdf: ^3.11.3
  open_file: ^3.5.10
  shared_preferences: ^2.5.2
  objectbox: ^4.1.0
  objectbox_flutter_libs: ^4.1.0
  mongo_dart: ^0.10.3
  connectivity_plus: ^6.1.3
  internet_connection_checker: ^3.0.1
  permission_handler: ^12.0.0+1
  logger: ^2.5.0
  google_fonts: ^6.2.1
  cached_network_image: ^3.3.0
  webview_flutter: ^4.10.0
  geolocator: ^13.0.2
  flutter_svg: ^2.0.17
  uuid: ^4.5.1
  dartz: ^0.10.1
  internet_connection_checker_plus: 2.7.0
  flutter_secure_storage: ^9.2.4
  dio: ^5.8.0+1
  http: ^1.2.2
  intl: ^0.20.2
  pretty_dio_logger: ^1.4.0
  fl_chart: ^0.70.2
  dropdown_button2: ^2.3.9
  aqua_ui:
    git:
      url: https://github.com/aquaconnect/aqua_ui.git
      ref: bf5475f162528bdfe43908f3e8d9732f7e6ce807

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  mockito: ^5.4.5
  build_runner: ^2.4.15
  objectbox_generator: ^4.1.0

  # Additional testing dependencies
  bloc_test: ^10.0.0
  mocktail: ^1.0.4
  fake_async: ^1.3.1
  test: ^1.25.8
  coverage: ^1.9.2
  path_provider_platform_interface: ^2.1.2
  plugin_platform_interface: ^2.1.8

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/svg/
    - assets/images/
    - assets/images/scheme/
    - assets/icons/
    - assets/html/
    - assets/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
