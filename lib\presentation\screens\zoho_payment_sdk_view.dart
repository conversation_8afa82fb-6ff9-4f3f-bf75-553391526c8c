import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import 'dart:developer' as developer;

import '../../domain/entities/payments/payment_config.dart';

class ZohoPaymentSDKView extends StatefulWidget {
  final PaymentConfig paymentConfig;
  final Function(bool success, String? transactionId) onPaymentComplete;

  const ZohoPaymentSDKView({
    super.key,
    required this.paymentConfig,
    required this.onPaymentComplete,
  });

  @override
  State<ZohoPaymentSDKView> createState() => _ZohoPaymentSDKViewState();
}

class _ZohoPaymentSDKViewState extends State<ZohoPaymentSDKView> {
  late WebViewController _controller;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    try {
      _controller = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..addJavaScriptChannel(
          'PaymentHandler',
          onMessageReceived: (JavaScriptMessage message) {
            _handlePaymentMessage(message.message);
          },
        )
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              setState(() {
                _isLoading = true;
                _errorMessage = null;
              });
            },
            onPageFinished: (String url) {
              setState(() {
                _isLoading = false;
              });
              // Send payment configuration to the web page
              _sendPaymentConfig();
            },
            onWebResourceError: (WebResourceError error) {
              setState(() {
                _isLoading = false;
                _errorMessage = 'Failed to load payment page: ${error.description}';
              });
            },
          ),
        );

      // Load the HTML content
      _loadPaymentHTML();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to initialize payment: $e';
      });
    }
  }

  Future<void> _loadPaymentHTML() async {
    try {
      // Load the HTML file from assets
      final htmlContent = await rootBundle.loadString('assets/html/zoho_payment_widget.html');
      await _controller.loadHtmlString(htmlContent);
    } catch (e) {
      developer.log('Error loading HTML: $e', name: 'ZohoPaymentSDKView');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load payment interface: $e';
      });
    }
  }

  void _sendPaymentConfig() {
    try {
      final configJson = jsonEncode(widget.paymentConfig.toJson());
      _controller.runJavaScript('''
        window.postMessage({
          type: 'PAYMENT_CONFIG',
          config: $configJson
        }, '*');
      ''');
      developer.log('Payment config sent: ${widget.paymentConfig}', name: 'ZohoPaymentSDKView');
    } catch (e) {
      developer.log('Error sending payment config: $e', name: 'ZohoPaymentSDKView');
    }
  }

  void _handlePaymentMessage(String message) {
    try {
      final data = jsonDecode(message);
      final type = data['type'] as String?;
      
      developer.log('Received payment message: $type', name: 'ZohoPaymentSDKView');
      
      switch (type) {
        case 'PAYMENT_SUCCESS':
          final transactionId = data['transactionId'] as String?;
          widget.onPaymentComplete(true, transactionId);
          break;
        case 'PAYMENT_FAILURE':
          widget.onPaymentComplete(false, null);
          break;
        case 'PAYMENT_CANCELLED':
          widget.onPaymentComplete(false, null);
          break;
        default:
          developer.log('Unknown payment message type: $type', name: 'ZohoPaymentSDKView');
      }
    } catch (e) {
      developer.log('Error handling payment message: $e', name: 'ZohoPaymentSDKView');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment'),
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () {
            widget.onPaymentComplete(false, null);
            Navigator.of(context).pop();
          },
        ),
      ),
      body: _errorMessage != null
          ? _buildErrorView()
          : Stack(
              children: [
                WebViewWidget(controller: _controller),
                if (_isLoading) const Center(child: CircularProgressIndicator()),
              ],
            ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Payment Error',
              style: Theme.of(context).textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _errorMessage = null;
                  _isLoading = true;
                });
                _initializeWebView();
              },
              child: const Text('Retry'),
            ),
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                widget.onPaymentComplete(false, null);
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }
}
