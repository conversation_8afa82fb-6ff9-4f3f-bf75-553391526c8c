import 'package:flutter_test/flutter_test.dart';

import '../../lib/domain/entities/payments/payment_config.dart';
import '../../lib/core/config/zoho_config.dart';
import '../../lib/data/models/payments/payment_session_model.dart';

void main() {
  group('Zoho SDK Integration Tests', () {
    test('should create valid payment configuration', () {
      // Arrange
      const sessionId = '****************';
      const amount = 100.0;
      const currency = 'INR';
      const description = 'Test payment';
      const invoiceNumber = 'INV-001';
      const customerId = 'CUST-001';

      // Act
      final config = PaymentConfig.fromPaymentData(
        sessionId: sessionId,
        amount: amount,
        currency: currency,
        description: description,
        invoiceNumber: invoiceNumber,
        customerId: customerId,
        customerName: 'Test Customer',
        customerEmail: '<EMAIL>',
        customerPhone: '+************',
      );

      // Assert
      expect(config.sessionId, sessionId);
      expect(config.amount, amount);
      expect(config.currency, currency);
      expect(config.description, description);
      expect(config.invoiceNumber, invoiceNumber);
      expect(config.customerId, customerId);
      expect(config.customerName, 'Test Customer');
      expect(config.customerEmail, '<EMAIL>');
      expect(config.customerPhone, '+************');
      expect(config.accountId, isNotEmpty);
      expect(config.apiKey, isNotEmpty);
    });

    test('should convert payment config to JSON correctly', () {
      // Arrange
      final config = PaymentConfig.fromPaymentData(
        sessionId: 'session_123',
        amount: 250.0,
        currency: 'INR',
        description: 'JSON test payment',
        invoiceNumber: 'INV-JSON-001',
        customerId: 'CUST-JSON-001',
      );

      // Act
      final json = config.toJson();

      // Assert
      expect(json['sessionId'], 'session_123');
      expect(json['amount'], '250.0');
      expect(json['currency'], 'INR');
      expect(json['description'], 'JSON test payment');
      expect(json['invoiceNumber'], 'INV-JSON-001');
      expect(json['customerId'], 'CUST-JSON-001');
      expect(json['accountId'], isNotNull);
      expect(json['apiKey'], isNotNull);
    });

    test('should create URL parameters correctly', () {
      // Arrange
      final config = PaymentConfig.fromPaymentData(
        sessionId: 'url_session_123',
        amount: 500.0,
        currency: 'INR',
        description: 'URL test payment',
        invoiceNumber: 'INV-URL-001',
        customerId: 'CUST-URL-001',
        customerName: 'URL Test Customer',
        customerEmail: '<EMAIL>',
      );

      // Act
      final urlParams = config.toUrlParameters();

      // Assert
      expect(urlParams, contains('sessionId=url_session_123'));
      expect(urlParams, contains('amount=500.0'));
      expect(urlParams, contains('currency=INR'));
      expect(urlParams, contains('description=URL%20test%20payment'));
      expect(urlParams, contains('customerName=URL%20Test%20Customer'));
      expect(urlParams, contains('customerEmail=url.test%40customer.com'));
    });

    test('should construct correct payment URL for SDK approach', () {
      // Arrange - Simulate API response without payment_url
      final json = {
        'payment_session': {
          'payments_session_id': '****************',
          'amount': '100.00',
          'currency': 'INR',
          'invoice_number': 'SDK-TEST-001',
          'created_time': 1640995200,
          // Note: no payment_url field
        },
        'data': {
          'customer_id': 'SDK-CUSTOMER-001',
        }
      };

      // Act
      final result = PaymentSessionModel.fromJson(json);

      // Assert
      expect(result.paymentUrl, contains('assets/html/zoho_payment_widget.html'));
      expect(result.paymentUrl, contains('sessionId=****************'));
      expect(result.sessionId, '****************');
    });

    test('should validate Zoho configuration status', () {
      // Act & Assert
      expect(ZohoConfig.accountId, isNotEmpty);
      expect(ZohoConfig.apiKey, isNotEmpty);
      expect(ZohoConfig.clientId, isNotEmpty);
      expect(ZohoConfig.clientSecret, isNotEmpty);
      expect(ZohoConfig.refreshToken, isNotEmpty);
      
      // Configuration status
      final status = ZohoConfig.configurationStatus;
      expect(status, isNotEmpty);
      
      // Default headers
      final headers = ZohoConfig.defaultHeaders;
      expect(headers['Content-Type'], 'application/json');
      expect(headers['Accept'], 'application/json');
      
      // Auth headers
      final authHeaders = ZohoConfig.getAuthHeaders('test_token');
      expect(authHeaders['Authorization'], 'Zoho-oauthtoken test_token');
    });

    test('should handle payment result creation', () {
      // Test success result
      final successResult = PaymentResult.success(
        transactionId: 'txn_123',
        paymentId: 'pay_456',
        additionalData: {'method': 'card'},
      );
      
      expect(successResult.status, PaymentStatus.success);
      expect(successResult.transactionId, 'txn_123');
      expect(successResult.paymentId, 'pay_456');
      expect(successResult.additionalData?['method'], 'card');
      
      // Test failure result
      final failureResult = PaymentResult.failure(
        errorMessage: 'Payment declined',
        additionalData: {'code': 'DECLINED'},
      );
      
      expect(failureResult.status, PaymentStatus.failure);
      expect(failureResult.errorMessage, 'Payment declined');
      expect(failureResult.additionalData?['code'], 'DECLINED');
      
      // Test cancelled result
      final cancelledResult = PaymentResult.cancelled();
      expect(cancelledResult.status, PaymentStatus.cancelled);
    });

    test('should serialize and deserialize payment result', () {
      // Arrange
      final originalResult = PaymentResult.success(
        transactionId: 'txn_serialize_123',
        paymentId: 'pay_serialize_456',
        additionalData: {'test': 'data'},
      );

      // Act
      final json = originalResult.toJson();
      final deserializedResult = PaymentResult.fromJson(json);

      // Assert
      expect(deserializedResult.status, originalResult.status);
      expect(deserializedResult.transactionId, originalResult.transactionId);
      expect(deserializedResult.paymentId, originalResult.paymentId);
      expect(deserializedResult.additionalData?['test'], 'data');
    });

    test('should handle different payment URL construction scenarios', () {
      final testCases = [
        {
          'description': 'API provides explicit payment_url',
          'json': {
            'payment_session': {
              'payments_session_id': 'session_explicit',
              'payment_url': 'https://custom.payment.url/session_explicit',
              'amount': '100.00',
              'currency': 'INR',
            },
            'data': {'customer_id': 'CUST-001'}
          },
          'expectedUrl': 'https://custom.payment.url/session_explicit',
        },
        {
          'description': 'API does not provide payment_url, construct SDK URL',
          'json': {
            'payment_session': {
              'payments_session_id': 'session_sdk',
              'amount': '200.00',
              'currency': 'INR',
            },
            'data': {'customer_id': 'CUST-002'}
          },
          'expectedUrlContains': 'assets/html/zoho_payment_widget.html?sessionId=session_sdk',
        },
        {
          'description': 'Empty session ID, return empty URL',
          'json': {
            'payment_session': {
              'amount': '300.00',
              'currency': 'INR',
            },
            'data': {'customer_id': 'CUST-003'}
          },
          'expectedUrl': '',
        },
      ];

      for (final testCase in testCases) {
        final json = testCase['json'] as Map<String, dynamic>;
        final result = PaymentSessionModel.fromJson(json);
        
        if (testCase.containsKey('expectedUrl')) {
          expect(result.paymentUrl, testCase['expectedUrl'],
                 reason: testCase['description'] as String);
        } else if (testCase.containsKey('expectedUrlContains')) {
          expect(result.paymentUrl, contains(testCase['expectedUrlContains']),
                 reason: testCase['description'] as String);
        }
      }
    });
  });
}
