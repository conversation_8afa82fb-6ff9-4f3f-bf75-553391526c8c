/// Configuration for Zoho Payments integration
class ZohoConfig {
  // TODO: Replace these with your actual Zoho Payments credentials
  // These should be obtained from your Zoho Payments account settings
  
  /// Zoho Account ID for Payments
  /// Get this from: Zoho Payments Dashboard > Settings > API Keys
  static const String accountId = String.fromEnvironment(
    'ZOHO_ACCOUNT_ID',
    defaultValue: 'YOUR_ZOHO_ACCOUNT_ID', // Replace with actual account ID
  );
  
  /// Zoho API Key for Payments
  /// Get this from: Zoho Payments Dashboard > Settings > API Keys
  static const String apiKey = String.fromEnvironment(
    'ZOHO_API_KEY', 
    defaultValue: 'YOUR_ZOHO_API_KEY', // Replace with actual API key
  );
  
  /// Zoho OAuth Client ID
  /// Get this from: Zoho Developer Console > Your App > Client Details
  static const String clientId = String.fromEnvironment(
    'ZOHO_CLIENT_ID',
    defaultValue: 'YOUR_ZOHO_CLIENT_ID', // Replace with actual client ID
  );
  
  /// Zoho OAuth Client Secret
  /// Get this from: Zoho Developer Console > Your App > Client Details
  static const String clientSecret = String.fromEnvironment(
    'ZOHO_CLIENT_SECRET',
    defaultValue: 'YOUR_ZOHO_CLIENT_SECRET', // Replace with actual client secret
  );
  
  /// Zoho OAuth Refresh Token
  /// Generate this using the OAuth flow or Zoho's token generation tools
  static const String refreshToken = String.fromEnvironment(
    'ZOHO_REFRESH_TOKEN',
    defaultValue: 'YOUR_ZOHO_REFRESH_TOKEN', // Replace with actual refresh token
  );
  
  /// Zoho API Base URL
  static const String apiBaseUrl = 'https://www.zohoapis.com';
  
  /// Zoho Payments API Base URL
  static const String paymentsApiBaseUrl = 'https://payments.zoho.com';
  
  /// Zoho OAuth Token URL
  static const String tokenUrl = 'https://accounts.zoho.com/oauth/v2/token';
  
  /// Validate that all required configuration is present
  static bool get isConfigured {
    return accountId != 'YOUR_ZOHO_ACCOUNT_ID' &&
           apiKey != 'YOUR_ZOHO_API_KEY' &&
           clientId != 'YOUR_ZOHO_CLIENT_ID' &&
           clientSecret != 'YOUR_ZOHO_CLIENT_SECRET' &&
           refreshToken != 'YOUR_ZOHO_REFRESH_TOKEN';
  }
  
  /// Get configuration status message
  static String get configurationStatus {
    if (isConfigured) {
      return 'Zoho configuration is complete';
    } else {
      final missing = <String>[];
      if (accountId == 'YOUR_ZOHO_ACCOUNT_ID') missing.add('Account ID');
      if (apiKey == 'YOUR_ZOHO_API_KEY') missing.add('API Key');
      if (clientId == 'YOUR_ZOHO_CLIENT_ID') missing.add('Client ID');
      if (clientSecret == 'YOUR_ZOHO_CLIENT_SECRET') missing.add('Client Secret');
      if (refreshToken == 'YOUR_ZOHO_REFRESH_TOKEN') missing.add('Refresh Token');
      
      return 'Missing Zoho configuration: ${missing.join(', ')}';
    }
  }
  
  /// Development/Testing configuration
  static bool get isDevelopment {
    return const bool.fromEnvironment('DEVELOPMENT', defaultValue: true);
  }
  
  /// Get headers for Zoho API requests
  static Map<String, String> get defaultHeaders {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
  }
  
  /// Get headers for authenticated Zoho API requests
  static Map<String, String> getAuthHeaders(String accessToken) {
    return {
      ...defaultHeaders,
      'Authorization': 'Zoho-oauthtoken $accessToken',
    };
  }
}

/// Instructions for setting up Zoho Payments configuration
class ZohoSetupInstructions {
  static const String instructions = '''
To configure Zoho Payments integration:

1. **Get Zoho Payments Account ID and API Key:**
   - Log in to your Zoho Payments account
   - Go to Settings > API Keys
   - Copy your Account ID and API Key

2. **Create Zoho OAuth App:**
   - Go to https://api-console.zoho.com/
   - Create a new app or use existing one
   - Note down Client ID and Client Secret

3. **Generate Refresh Token:**
   - Use Zoho's OAuth flow to generate a refresh token
   - Scopes needed: ZohoPayments.payments.ALL

4. **Set Environment Variables:**
   - ZOHO_ACCOUNT_ID=your_account_id
   - ZOHO_API_KEY=your_api_key
   - ZOHO_CLIENT_ID=your_client_id
   - ZOHO_CLIENT_SECRET=your_client_secret
   - ZOHO_REFRESH_TOKEN=your_refresh_token

5. **Update Configuration:**
   - Replace placeholder values in ZohoConfig class
   - Or use environment variables for security

For detailed setup instructions, refer to:
- Zoho Payments API Documentation
- Zoho OAuth Documentation
''';
}
